# ProMatos Mobile Application - Product Architecture 🎯

## Executive Summary

ProMatos is a revolutionary mobile application designed to transform electrical resource access across Africa through contextual AI, community solidarity, and peer-to-peer connections. This document outlines the complete product architecture, technical specifications, and implementation strategy.

## 1. Product Vision & Core Value Proposition

### Vision Statement
"Transform any electrical need into instant local and community-driven action"

### Core Value Propositions
- **Instant Access**: Connect users with electrical resources in real-time
- **Community-Driven**: Leverage local networks and peer-to-peer connections
- **Contextual Intelligence**: AI-powered understanding of user needs and local context
- **Inclusive Design**: Accessible across all economic segments and technical literacy levels
- **Safety-First**: Prioritize electrical safety and regulatory compliance

## 2. Target User Segments

### Primary Users
1. **Homeowners & Tenants**: Residential electrical needs and emergencies
2. **Small Business Owners**: Commercial electrical installations and maintenance
3. **Professional Electricians**: Service providers seeking customers and resources
4. **Electrical Suppliers**: Local shops and distributors
5. **Community Leaders**: Neighborhood coordinators and safety advocates

### User Personas
- **<PERSON><PERSON> (Homeowner, Dakar)**: Needs reliable electrician for home wiring
- **<PERSON><PERSON><PERSON> (Electrician, Abidjan)**: Seeks steady work and professional network
- **<PERSON><PERSON> (Shop Owner, Bamako)**: Requires electrical installation for new business
- **Ibrahim (Supplier, Lagos)**: Wants to reach more customers and reduce inventory

## 3. Core Features Architecture

### 3.1 Contextual Electrical Intelligence
```
Natural Language Interface
├── Voice Recognition (French, Wolof, Arabic, Local Dialects)
├── Text Input with Auto-completion
├── Image Recognition (Electrical Components)
└── Context Analysis Engine
    ├── Geolocation Services
    ├── Urgency Detection
    ├── Weather Integration
    ├── Local Standards Database
    └── User Profile Analysis
```

### 3.2 Community Electrician Network
```
Matching System
├── Real-time Availability Tracking
├── Skill-based Matching
├── Distance Optimization
├── Reputation Scoring
└── Communication Hub
    ├── In-app Messaging
    ├── Voice/Video Calls
    ├── File Sharing (Diagrams, Photos)
    └── Translation Services
```

### 3.3 Solidarity Marketplace
```
Sharing Economy Platform
├── Tool Lending/Rental
├── Material Exchange
├── Skill Bartering
├── Community Bulk Purchasing
└── Alternative Payment Systems
    ├── Time Banking
    ├── Service Exchange
    ├── Community Currency
    └── Traditional Payment
```

### 3.4 Safety & Prevention System
```
Safety Intelligence
├── Proactive Alerts
├── Hazard Detection
├── Safety Education
├── Compliance Checking
└── Emergency Response
    ├── Quick Emergency Contacts
    ├── Safety Protocol Guidance
    ├── Community Alert System
    └── Professional Emergency Services
```

## 4. Technical Architecture Overview

### 4.1 System Architecture
```
Mobile Application (Flutter)
├── Presentation Layer
├── Business Logic Layer
├── Data Access Layer
└── Offline Capability Layer

Backend Services (Node.js/Python)
├── API Gateway
├── Authentication Service
├── Matching Engine
├── Payment Processing
├── Notification Service
└── AI/ML Services

Database (Supabase)
├── User Management
├── Service Requests
├── Electrician Profiles
├── Marketplace Items
├── Safety Data
└── Analytics
```

### 4.2 Key Technologies
- **Mobile**: Flutter for cross-platform development
- **Backend**: Node.js with Express.js or Python with FastAPI
- **Database**: Supabase (PostgreSQL) with real-time subscriptions
- **AI/ML**: OpenAI GPT for NLP, TensorFlow Lite for mobile ML
- **Maps**: Google Maps API with offline capabilities
- **Payments**: Stripe, Wave, Orange Money, MTN Mobile Money
- **Communication**: WebRTC for voice/video, Socket.io for real-time messaging

## 5. Database Schema (Supabase)

### Core Tables
```sql
-- Users table
users (
  id UUID PRIMARY KEY,
  email VARCHAR UNIQUE,
  phone VARCHAR UNIQUE,
  full_name VARCHAR,
  user_type ENUM('customer', 'electrician', 'supplier'),
  location GEOGRAPHY(POINT),
  languages VARCHAR[],
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)

-- Electrician profiles
electrician_profiles (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  certifications JSONB,
  specializations VARCHAR[],
  experience_years INTEGER,
  service_radius INTEGER,
  hourly_rate DECIMAL,
  availability_schedule JSONB,
  rating DECIMAL,
  total_jobs INTEGER
)

-- Service requests
service_requests (
  id UUID PRIMARY KEY,
  customer_id UUID REFERENCES users(id),
  electrician_id UUID REFERENCES users(id),
  title VARCHAR,
  description TEXT,
  urgency_level ENUM('low', 'medium', 'high', 'emergency'),
  location GEOGRAPHY(POINT),
  estimated_budget DECIMAL,
  status ENUM('pending', 'matched', 'in_progress', 'completed', 'cancelled'),
  created_at TIMESTAMP,
  scheduled_at TIMESTAMP
)
```

## 6. API Specifications

### Core Endpoints
```
Authentication
POST /auth/register
POST /auth/login
POST /auth/refresh

User Management
GET /users/profile
PUT /users/profile
GET /users/electricians/nearby

Service Requests
POST /requests
GET /requests
PUT /requests/:id/accept
PUT /requests/:id/complete

Marketplace
GET /marketplace/items
POST /marketplace/items
PUT /marketplace/items/:id

Safety
GET /safety/alerts
POST /safety/reports
GET /safety/guidelines
```

## 7. Mobile App Structure

### Screen Architecture
```
App Navigation
├── Authentication Flow
│   ├── Welcome Screen
│   ├── Registration
│   └── Login
├── Main App (Bottom Navigation)
│   ├── Home (Contextual AI Interface)
│   ├── Services (Electrician Network)
│   ├── Marketplace (Solidarity Economy)
│   ├── Safety (Prevention & Alerts)
│   └── Profile (User Management)
└── Feature Flows
    ├── Service Request Flow
    ├── Electrician Matching Flow
    ├── Marketplace Transaction Flow
    └── Emergency Response Flow
```

## 8. Offline Capabilities

### Offline-First Design
- **Local Database**: SQLite for offline data storage
- **Sync Strategy**: Intelligent synchronization when connectivity returns
- **Mesh Networking**: Bluetooth-based local communication
- **Cached Content**: Essential safety information and local contacts
- **Progressive Web App**: Fallback for basic phone browsers

## 9. Multilingual & Accessibility

### Language Support
- **Primary**: French (official language across Francophone Africa)
- **Secondary**: English, Arabic
- **Local**: Wolof (Senegal), Bambara (Mali), Yoruba (Nigeria), Swahili (East Africa)

### Accessibility Features
- **Voice Interface**: Speech-to-text and text-to-speech
- **Simple Mode**: Simplified UI for low-literacy users
- **Large Text**: Adjustable font sizes
- **High Contrast**: Visual accessibility options
- **Offline Voice**: Local speech recognition for basic commands
